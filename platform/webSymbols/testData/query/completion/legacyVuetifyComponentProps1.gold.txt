[
 {
  name: "color",
  priority: NORMAL,
  source: {
   matchedName: "html/props/color",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "color",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "dismissible",
  priority: NORMAL,
  source: {
   matchedName: "html/props/dismissible",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "dismissible",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "icon",
  priority: NORMAL,
  source: {
   matchedName: "html/props/icon",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "icon",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "mode",
  priority: NORMAL,
  source: {
   matchedName: "html/props/mode",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "mode",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "origin",
  priority: NORMAL,
  source: {
   matchedName: "html/props/origin",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "origin",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "outline",
  priority: NORMAL,
  source: {
   matchedName: "html/props/outline",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "outline",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "transition",
  priority: NORMAL,
  source: {
   matchedName: "html/props/transition",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "transition",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "type",
  priority: NORMAL,
  source: {
   matchedName: "html/props/type",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "type",
     symbol: <self>,
    },
   ],
  },
 },
 {
  name: "value",
  priority: NORMAL,
  source: {
   matchedName: "html/props/value",
   origin: "vuetify@1.3.2 (vue)",
   complete: true,
   priority: NORMAL,
   segments: [
    {
     name-part: "value",
     symbol: <self>,
    },
   ],
  },
 },
]
