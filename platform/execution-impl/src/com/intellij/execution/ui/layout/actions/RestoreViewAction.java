// Copyright 2000-2022 JetBrains s.r.o. and contributors. Use of this source code is governed by the Apache 2.0 license.

package com.intellij.execution.ui.layout.actions;

import com.intellij.execution.ui.layout.impl.RunnerContentUi;
import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareToggleAction;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.custom.options.ContentLayoutStateSettings;
import org.jetbrains.annotations.ApiStatus;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;

@ApiStatus.Internal
public final class RestoreViewAction extends DumbAwareToggleAction implements ViewLayoutModificationAction {

  private final Content myContent;
  private final ContentLayoutStateSettings myLayoutSettings;

  public RestoreViewAction(@NotNull RunnerContentUi ui, @NotNull Content content) {
    this(content, new DefaultContentStateSettings(ui, content));
  }

  public RestoreViewAction(@NotNull Content content, ContentLayoutStateSettings layoutSettings) {
    myContent = content;
    myLayoutSettings = layoutSettings;
  }

  @Override
  public boolean isSelected(@NotNull AnActionEvent e) {
    return myLayoutSettings.isSelected();
  }

  @Override
  public @NotNull ActionUpdateThread getActionUpdateThread() {
    return ActionUpdateThread.EDT;
  }

  @Override
  public void setSelected(@NotNull AnActionEvent e, boolean state) {
    myLayoutSettings.setSelected(state);
  }

  @Override
  public void update(final @NotNull AnActionEvent e) {
    super.update(e);
    e.getPresentation().setText(myLayoutSettings.getDisplayName(), false);
    e.getPresentation().setEnabled(myLayoutSettings.isEnabled());
  }

  public @NotNull Content getContent() {
    return myContent;
  }

  private static final class DefaultContentStateSettings implements ContentLayoutStateSettings {

    private final RunnerContentUi myUi;
    private final Content myContent;

    public DefaultContentStateSettings(@NotNull RunnerContentUi ui,
                                       @NotNull Content content) {
      myUi = ui;
      myContent = content;
    }

    @Override
    public boolean isSelected() {
      return myContent.isValid() && Objects.requireNonNull(myContent.getManager()).getIndexOfContent(myContent) != -1;
    }

    @Override
    public void setSelected(boolean state) {
      if (state) {
        myUi.restore(myContent);
        myUi.select(myContent, true);
      } else {
        myUi.minimize(myContent, null);
      }
    }

    @Override
    public void restore() {
      setSelected(true);
    }

    @Override
    public @NotNull String getDisplayName() {
      return myContent.getDisplayName();
    }

    @Override
    public boolean isEnabled() {
      return !isSelected() || myUi.getContentManager().getContents().length > 1;
    }
  }
}
