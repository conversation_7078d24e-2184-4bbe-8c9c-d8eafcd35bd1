com.intellij.platform.backend.observation.ActivityKey
- a:getPresentableName():java.lang.String
com.intellij.platform.backend.observation.ActivityTracker
- a:awaitConfiguration(com.intellij.openapi.project.Project,kotlin.coroutines.Continuation):java.lang.Object
- a:getPresentableName():java.lang.String
- a:isInProgress(com.intellij.openapi.project.Project,kotlin.coroutines.Continuation):java.lang.Object
f:com.intellij.platform.backend.observation.Observation
- sf:INSTANCE:com.intellij.platform.backend.observation.Observation
- f:awaitConfiguration(com.intellij.openapi.project.Project,kotlin.jvm.functions.Function1,kotlin.coroutines.Continuation):java.lang.Object
- bs:awaitConfiguration$default(com.intellij.platform.backend.observation.Observation,com.intellij.openapi.project.Project,kotlin.jvm.functions.Function1,kotlin.coroutines.Continuation,I,java.lang.Object):java.lang.Object
- f:configurationFlow(com.intellij.openapi.project.Project):kotlinx.coroutines.flow.StateFlow
f:com.intellij.platform.backend.observation.TrackingUtil
- sf:launchTracked(kotlinx.coroutines.CoroutineScope,kotlin.coroutines.CoroutineContext,kotlin.jvm.functions.Function2):V
- bs:launchTracked$default(kotlinx.coroutines.CoroutineScope,kotlin.coroutines.CoroutineContext,kotlin.jvm.functions.Function2,I,java.lang.Object):V
- sf:trackActivity(com.intellij.openapi.project.Project,com.intellij.platform.backend.observation.ActivityKey,java.lang.Runnable):V
- sf:trackActivity(com.intellij.openapi.project.Project,com.intellij.platform.backend.observation.ActivityKey,kotlin.jvm.functions.Function1,kotlin.coroutines.Continuation):java.lang.Object
- sf:trackActivityBlocking(com.intellij.openapi.project.Project,com.intellij.platform.backend.observation.ActivityKey,kotlin.jvm.functions.Function0):java.lang.Object
