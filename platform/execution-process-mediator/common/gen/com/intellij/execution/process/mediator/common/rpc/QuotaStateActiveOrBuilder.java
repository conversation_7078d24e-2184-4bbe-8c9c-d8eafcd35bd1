// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: processMediator.proto

package com.intellij.execution.process.mediator.common.rpc;

public interface QuotaStateActiveOrBuilder extends
    // @@protoc_insertion_point(interface_extends:intellij.process.mediator.common.rpc.QuotaStateActive)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 elapsed_ms = 1;</code>
   * @return The elapsedMs.
   */
  long getElapsedMs();
}
