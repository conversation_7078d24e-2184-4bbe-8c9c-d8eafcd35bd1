// Copyright 2000-2023 JetBrains s.r.o. and contributors. Use of this source code is governed by the Apache 2.0 license.
package com.intellij.formatting.templateLanguages;

import com.intellij.formatting.*;
import com.intellij.lang.ASTNode;
import com.intellij.lang.Language;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiElement;
import com.intellij.psi.formatter.common.AbstractBlock;
import com.intellij.psi.templateLanguages.OuterLanguageElement;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class DataLanguageBlockWrapper implements ASTBlock, BlockEx, BlockWithParent {
  private final Block myOriginal;
  private final @Nullable Language myLanguage;
  private List<Block> myBlocks;
  private List<TemplateLanguageBlock> myTlBlocks;
  private BlockWithParent myParent;
  private DataLanguageBlockWrapper myRightHandWrapper;
  private Spacing mySpacing;
  private Map<Pair<Block, Block>, Spacing> myChildDataBorderSpacings;

  private DataLanguageBlockWrapper(final @NotNull Block original) {
    assert !(original instanceof DataLanguageBlockWrapper) && !(original instanceof TemplateLanguageBlock);
    myOriginal = original;

    final ASTNode node = getNode();
    Language language = null;
    if (node != null) {
      final PsiElement psi = node.getPsi();
      if (psi != null) {
        language = psi.getContainingFile().getLanguage();
      }
    }
    myLanguage = language;
  }

  @Override
  public @NotNull TextRange getTextRange() {
    return myOriginal.getTextRange();
  }

  @Override
  public @NotNull List<Block> getSubBlocks() {
    if (myBlocks == null) {
      myBlocks = buildBlocks();
      initSpacings();
    }
    return myBlocks;
  }

  private void initSpacings() {
    for (int i = 0; i < myBlocks.size(); i++) {
      Block block1 = i == 0 ? null : myBlocks.get(i - 1);
      Block block2 = myBlocks.get(i);
      Spacing spacing = calcChildSpacing(i, block1, block2);
      if (spacing != null) {
        registerChildSpacing(block1, block2, spacing);
      }
    }
  }

  private @Nullable Spacing calcChildSpacing(int index2, @Nullable Block block1, @NotNull Block block2) {
    if (block1 instanceof TemplateLanguageBlock) {
      Spacing spacing = ((TemplateLanguageBlock)block1).getRightNeighborSpacing(block2, this, index2 - 1);
      if (spacing != null) return spacing;
    }
    if (block2 instanceof TemplateLanguageBlock) {
      return ((TemplateLanguageBlock)block2).getLeftNeighborSpacing(block1, this, index2);
    }
    return null;
  }

  private void registerChildSpacing(@Nullable Block block1, @NotNull Block block2, Spacing spacing) {
    if (myChildDataBorderSpacings == null) {
      myChildDataBorderSpacings = new HashMap<>();
    }
    myChildDataBorderSpacings.put(new Pair<>(block1, block2), spacing);
  }

  @Override
  public @Nullable Language getLanguage() {
    // Use base language code style settings for the template blocks.
    return myLanguage;
  }

  private List<Block> buildBlocks() {
    assert myBlocks == null;
    if (isLeaf()) {
      return AbstractBlock.EMPTY;
    }
    final List<DataLanguageBlockWrapper> subWrappers = BlockUtil.buildChildWrappers(myOriginal);
    final List<Block> children;
    if (myTlBlocks == null) {
      children = new ArrayList<>(subWrappers);
    }
    else if (subWrappers.size() == 0) {
      children = BlockUtil.splitBlockIntoFragments(myOriginal, myTlBlocks);
    }
    else {
      children = BlockUtil.mergeBlocks(myTlBlocks, subWrappers);
    }
    //BlockUtil.printBlocks(getTextRange(), children);
    return BlockUtil.setParent(children, this);
  }

  @Override
  public Wrap getWrap() {
    BlockWithParent parent = getParent();
    if (parent instanceof TemplateLanguageBlock) {
      return ((TemplateLanguageBlock)parent).substituteTemplateChildWrap(this, myOriginal.getWrap());
    }
    return myOriginal.getWrap();
  }

  @Override
  public @NotNull ChildAttributes getChildAttributes(final int newChildIndex) {
    return myOriginal.getChildAttributes(newChildIndex);
  }

  @Override
  public Indent getIndent() {
    return myOriginal.getIndent();
  }

  @Override
  public Alignment getAlignment() {
    return myOriginal.getAlignment();
  }

  @Override
  public @Nullable Spacing getSpacing(Block child1, @NotNull Block child2) {
    if (child1 instanceof DataLanguageBlockWrapper && child2 instanceof DataLanguageBlockWrapper) {
      return myOriginal.getSpacing(((DataLanguageBlockWrapper)child1).myOriginal, ((DataLanguageBlockWrapper)child2).myOriginal);
    }
    if ((child1 instanceof TemplateLanguageBlock || child2 instanceof TemplateLanguageBlock) && myChildDataBorderSpacings != null) {
      return myChildDataBorderSpacings.get(Pair.create(child1, child2));
    }
   return null;
  }

  @Override
  public boolean isIncomplete() {
    return myOriginal.isIncomplete();
  }

  @Override
  public boolean isLeaf() {
    return myTlBlocks == null && myOriginal.isLeaf();
  }

  void addTlChild(TemplateLanguageBlock tlBlock) {
    assert myBlocks == null;
    if (myTlBlocks == null) {
      myTlBlocks = new ArrayList<>(5);
    }
    myTlBlocks.add(tlBlock);
    tlBlock.setParent(this);
  }

  public Block getOriginal() {
    return myOriginal;
  }

  @Override
  public String toString() {
    String tlBlocksInfo = " TlBlocks " + (myTlBlocks == null ? "0" : myTlBlocks.size()) + "|" + getTextRange() + "|";
    return tlBlocksInfo + myOriginal.toString();
  }

  public static @Nullable DataLanguageBlockWrapper create(final @NotNull Block original, final @Nullable Indent indent) {
    final boolean doesntNeedWrapper = original instanceof ASTBlock && ((ASTBlock)original).getNode() instanceof OuterLanguageElement;
    return doesntNeedWrapper ? null : new DataLanguageBlockWrapper(original);
  }

  @Override
  public @Nullable ASTNode getNode() {
    return myOriginal instanceof ASTBlock ? ((ASTBlock)myOriginal).getNode() : null;
  }

  @Override
  public BlockWithParent getParent() {
    return myParent;
  }

  @Override
  public void setParent(BlockWithParent parent) {
    myParent = parent;
  }

  public void setRightHandSpacing(DataLanguageBlockWrapper rightHandWrapper, Spacing spacing) {
    myRightHandWrapper = rightHandWrapper;
    mySpacing = spacing;
  }

  public @Nullable Spacing getRightHandSpacing(DataLanguageBlockWrapper rightHandWrapper) {
    return myRightHandWrapper == rightHandWrapper ? mySpacing : null;
  }
}
