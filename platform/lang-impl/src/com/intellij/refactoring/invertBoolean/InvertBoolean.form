<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.intellij.refactoring.invertBoolean.InvertBooleanDialog">
  <grid id="3c0f9" binding="myPanel" row-count="2" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="0" left="0" bottom="0" right="0"/>
    <constraints>
      <xy x="54" y="99" width="434" height="60"/>
      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <component id="5b95d" class="javax.swing.JLabel" binding="myLabel">
        <constraints>
          <xy x="2" y="45" width="0" height="0"/>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0"/>
        </constraints>
        <properties>
          <inheritsPopupMenu value="false"/>
          <nextFocusableComponent value=""/>
          <text value=""/>
        </properties>
      </component>
      <component id="ceab0" class="javax.swing.JTextField" binding="myNameField">
        <constraints>
          <xy x="32" y="35" width="400" height="20"/>
          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0">
            <preferred-size width="150" height="-1"/>
          </grid>
        </constraints>
        <properties/>
      </component>
      <component id="42aac" class="javax.swing.JLabel" binding="myCaptionLabel">
        <constraints>
          <xy x="2" y="15" width="0" height="0"/>
          <grid row="0" column="0" row-span="1" col-span="2" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0"/>
        </constraints>
        <properties>
          <text value=""/>
        </properties>
      </component>
    </children>
  </grid>
</form>
