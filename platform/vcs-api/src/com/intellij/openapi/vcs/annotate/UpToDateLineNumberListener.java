/*
 * Copyright 2000-2015 JetBrains s.r.o.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.intellij.openapi.vcs.annotate;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.util.Consumer;

/**
 * Interface that can be implemented by {@link AnAction} in vcs Annotations popup menu.
 * The setter will be called when popup is shown, passing the clicked 'annotated line' (not to be confused with 'editor line').
 *
 * @deprecated Prefer using {@link com.intellij.openapi.vcs.actions.ShowAnnotateOperationsPopup#getAnnotationLineNumber(DataContext)} instead.
 */
@Deprecated
public interface UpToDateLineNumberListener extends Consumer<Integer> {
}
