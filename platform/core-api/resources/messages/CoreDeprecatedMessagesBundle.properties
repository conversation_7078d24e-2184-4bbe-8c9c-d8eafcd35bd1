# suppress inspection "UnusedProperty" for whole file
#this bundle is DEPRECATED, it's kept for compatibility with external plugins only
#there is no need to translate messages from it

#messages from PsiBundle
psi.error.incorrect.class.template.message=Cannot create {0} - incorrect {1} template.
psi.search.inheritors.of.class.progress=Searching for inheritors of {0}\u2026
psi.search.inheritors.progress=Searching for inheritors\u2026
psi.search.for.word.progress=Searching for {0}\u2026
psi.search.scope.project=Project Files
search.scope.module=Module ''{0}''
search.scope.module.runtime=Module ''{0}'' runtime scope
class.context.display={0} in {1}
aux.context.display=(in {0})
0.is.not.an.identifier=''{0}'' is not an identifier.
cannot.modify.a.read.only.file=Cannot modify a read-only file ''{0}''.

#messages from FindBundle
find.what.methods.usages.checkbox=Usages of &methods
find.what.fields.usages.checkbox=Usages of &fields
find.what.implementing.classes.checkbox=&Implementing classes
find.what.derived.interfaces.checkbox=&Derived interfaces
find.what.derived.classes.checkbox=&Derived classes
find.what.implementing.methods.checkbox=&Implementing methods
find.what.overriding.methods.checkbox=Over&riding methods
find.what.group=Find
find.options.include.overloaded.methods.checkbox=Include o&verloaded methods
find.parameter.usages.in.overriding.methods.prompt=Do you want to search usages of parameter ''{0}'' in overriding methods?
find.parameter.usages.in.overriding.methods.title=Search in Overriding Methods
find.usages.panel.title.separator=or
find.usages.panel.title.usages=Usages
occurrence=occurrence
find.options.group=General
find.options.skip.results.tab.with.one.usage.action=S&kip Results Tab with One Usage

#messages from AnalysisScopeBundle
specify.analysis.scope=Specify {0} Scope
analysis.scope.title={0} scope
checking.class.files=Checking Class Files
recompile.confirmation.message=The project's class files are out of date. Do you want to compile the project before continuing DSM analysis?\nNot compiling may result in incomplete or incorrect results.
project.is.out.of.date=Project Is Out Of Date
