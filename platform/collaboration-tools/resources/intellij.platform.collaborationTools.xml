<idea-plugin>
  <dependencies>
    <module name="intellij.platform.vcs.dvcs.impl"/>
    <module name="intellij.platform.vcs.log.impl"/>
  </dependencies>
  <extensions defaultExtensionNs="com.intellij">
    <themeMetadataProvider path="/META-INF/CollaborationTools.themeMetadata.json"/>
    <statistics.notificationIdsHolder implementation="com.intellij.collaboration.ui.notification.CollaborationToolsNotificationIdsHolder"/>
    <notificationGroup id="VCS Hosting Integrations"
                       displayType="BALLOON"
                       bundle="messages.CollaborationToolsBundle"
                       key="notification.group.vcs.hosting.integrations"/>

    <diff.actions.ShowDiffAction.ExtensionProvider
      implementation="com.intellij.collaboration.ui.codereview.action.CodeReviewShowDiffActionProvider$Preview"/>
    <!--<diff.actions.ShowStandaloneDiffAction.ExtensionProvider
      implementation="com.intellij.collaboration.ui.codereview.action.CodeReviewShowDiffActionProvider$Standalone"/>-->

    <advancedSetting id="enable.combined.diff.for.codereview"
                     default="true"
                     bundle="messages.CollaborationToolsBundle"
                     groupKey="advanced.settings.collab.tools"/>
  </extensions>
  <applicationListeners>
    <listener class="com.intellij.collaboration.ui.codereview.CodeReviewCombinedDiffAdvancedSettingsChangeListener"
              topic="com.intellij.openapi.options.advanced.AdvancedSettingsChangeListener"/>
  </applicationListeners>
  <actions>
    <action id="Code.Review.Editor.Show.Diff"
            class="com.intellij.collaboration.ui.codereview.editor.action.CodeReviewEditorShowFileDiffAction"
            icon="AllIcons.Actions.Diff">
      <add-to-group group-id="EditorPopupMenu" anchor="last"/>
    </action>

    <action id="Code.Review.Editor.New.Comment"
            class="com.intellij.collaboration.ui.codereview.editor.action.CodeReviewEditorNewCommentAction">
      <keyboard-shortcut first-keystroke="control shift X" keymap="$default"/>
      <keyboard-shortcut first-keystroke="control shift X" keymap="Eclipse" remove="true"/>
      <keyboard-shortcut first-keystroke="control shift X" keymap="Eclipse (Mac OS X)" remove="true"/>
      <keyboard-shortcut first-keystroke="meta shift X" keymap="Eclipse (Mac OS X)" remove="true"/>
      <keyboard-shortcut first-keystroke="control shift X" keymap="Emacs" remove="true"/>
      <add-to-group group-id="EditorPopupMenu" anchor="last"/>
      <add-to-group group-id="Diff.EditorPopupMenu" anchor="last"/>
    </action>
  </actions>
</idea-plugin>